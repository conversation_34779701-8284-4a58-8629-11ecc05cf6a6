name: Star

on:

  push:
    branches:
      - main
      
  schedule:
  - cron: "* * * * *"

jobs:
  auto_commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
         persist-credentials: false
         fetch-depth: 0

      - name: Modify last update
        run: |
          d='DATE '`date '+%Y-%m-%d %H:%M:%S'`
          echo $d > TikTok
          
      - name: Commit changes
        run: | 
          git config --local user.email "<EMAIL>"
          git config --local user.name "${{ github.repository_owner }}"
          git add -A
          
          arr[0]="TikTok Report Bot"
          arr[1]="TikTok"
          
          rand=$[$RANDOM % ${#arr[@]}]
          
          git commit -m "${arr[$rand]}"
          
      - name: GitHub Push
        uses: ad-m/github-push-action@master
        with:
          force: true
          directory: "."
          github_token: ${{ secrets.GITHUB_TOKEN }}